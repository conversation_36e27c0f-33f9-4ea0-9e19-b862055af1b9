import React from 'react'
import { useNavigate } from 'react-router-dom'
import { SvgIcon } from './SvgIcon'
import { cn } from '@/utils/shad'

/**
 * 通用导航栏组件
 * 包含左侧返回按钮、中间标题、右侧可选扩展组件
 */
export interface CommonNavBarProps {
  /** 标题文本 */
  title: string
  /** 右侧扩展组件 */
  extra?: React.ReactNode
  /** 自定义返回逻辑，返回true表示已处理，不执行默认返回 */
  onBack?: () => boolean | void
  /** 导航栏容器类名 */
  className?: string
  /** 标题类名 */
  titleClassName?: string
  /** 返回按钮类名 */
  backButtonClassName?: string
  /** 右侧扩展区域类名 */
  extraClassName?: string
}

export const CommonNavBar: React.FC<CommonNavBarProps> = ({
  title,
  extra,
  onBack,
  className,
  titleClassName,
  backButtonClassName,
  extraClassName,
}) => {
  const navigate = useNavigate()

  const handleBack = () => {
    // 如果有自定义返回逻辑且返回true，则不执行默认返回
    if (onBack && onBack() === true) {
      return
    }
    // 默认返回上一页
    navigate(-1)
  }

  return (
    <div
      className={cn(
        'flex items-center justify-between w-full h-16 px-4 bg-[#141828]',
        className
      )}
    >
      {/* 左侧返回按钮 */}
      <div
        className={cn(
          'flex items-center justify-center w-12 h-12 cursor-pointer hover:bg-white/10 rounded-lg transition-colors',
          backButtonClassName
        )}
        onClick={handleBack}
      >
        <SvgIcon
          src="/images/icons/arrow-left.svg"
          alt="返回"
          className="w-6 h-6"
          svgClassName="text-white"
        />
      </div>

      {/* 中间标题 */}
      <div
        className={cn(
          'flex-1 text-center text-white text-lg font-medium truncate mx-4',
          titleClassName
        )}
      >
        {title}
      </div>

      {/* 右侧扩展区域 */}
      <div
        className={cn(
          'flex items-center justify-center w-12 h-12',
          extraClassName
        )}
      >
        {extra}
      </div>
    </div>
  )
}
