import { use<PERSON><PERSON>, useAtomValue, useSet<PERSON>tom } from 'jotai'
import {
  resourceTemplateAtom,
  screenOrientationAtom,
  isTaskTypeSelectedAtom,
  isSupportVideoAtom,
  isShowThemeDetailModalAtom,
  selectedThemeDetailAtom,
  selectedGender<PERSON>tom,
  selectedEventDetailAtom,
} from '@/stores'
// import { AutoScroll } from '@/components/ui/AutoScroll'
import { useEffect, useMemo, useState } from 'react'
import { MirrorSexEnum, PopulationCategory } from '@/graphqls/types'
// import classnames from 'classnames'
import { ActiveTemplateItem } from '@/components/pages/homepage/const'
import SingleTemplateList from '@/components/pages/homepage/SingleTemplateList'
import MazeSingleTemplateList from '@/components/pages/homepage/MazeSingleTemplateList'
import MultipleTemplateList from '@/components/pages/homepage/MultipleTemplateList'
import classNames from 'classnames'
import { CreateBtn } from './CreateBtn'
import BackToHome from '@/components/business/BackToHome'
import { useTranslation } from 'react-i18next'
import { ThemeDetailModal } from './ThemeDetailModal'
import { ThemeDetail } from '@/apis/types'
import { isMachine, isPhone } from '@/utils'
import useSWR from 'swr'
import _api from '@/apis/maze.api'
import _ajax from '@/utils/ajax'
import { AutoScroll } from '@/components/ui/AutoScroll'

export const PictureFramework = () => {
  const screenOrientation = useAtomValue(screenOrientationAtom)
  const [, setIsTaskTypeSelected] = useAtom(isTaskTypeSelectedAtom)
  const [isSupportVideo] = useAtom(isSupportVideoAtom)
  const { t } = useTranslation()

  const genders = [
    {
      label: '女性',
      value: MirrorSexEnum.FEMALE,
    },
    {
      label: '男性',
      value: MirrorSexEnum.MALE,
    },
  ]

  // 选中的模板
  const [activeTemplate, setActiveTemplate] = useState<
    ThemeDetail | undefined | null
  >()
  // 选中的性别
  const [activeGender, setActiveGender] = useAtom(selectedGenderAtom)
  // 选中的 tag id
  const [activeTab, setActiveTab] = useState<number>(0)

  const [resourceTemplate] = useAtom(resourceTemplateAtom)

  const [themeDetailModalOpen, setThemeDetailModalOpen] = useAtom(
    isShowThemeDetailModalAtom
  )

  const setSelectedThemeDetail = useSetAtom(selectedThemeDetailAtom)
  const [selectedEventDetail] = useAtom(selectedEventDetailAtom)

  useEffect(() => {
    if (activeTemplate) {
      setSelectedThemeDetail(activeTemplate)
    }
  }, [activeTemplate])

  const categoryList = useMemo(() => {
    return (
      selectedEventDetail?.tags?.map((it, i) => ({
        label: it.name as string,
        value: i as number,
        id: it.id as number,
      })) || []
    )
  }, [selectedEventDetail])

  // 使用所有分类下的模版并去重
  const selectTemplateList = useMemo(() => {
    const selectedThemes = selectedEventDetail?.tags?.[activeTab]?.themes || []
    return selectedThemes
  }, [selectedEventDetail, activeTab])

  // console.log(selectTemplateList, resourceTemplate)

  // 设置默认选中模板
  useEffect(() => {
    setActiveTemplate(selectTemplateList?.[0])
  }, [selectTemplateList])

  return (
    <>
      <div className="w-full h-full p-[16px] pt-0">
        <div className="sticky top-0 z-10 pt-[16px] bg-[#141828]">
          <div className="flex text-white w-full justify-between leading-[18px]">
            <span className="text-[18px] text-white font-medium">
              AI Models
            </span>
            <span className="text-[2rem] ">3151</span>
          </div>
          {categoryList.length > 1 && (
            <div className={classNames('mt-8 mx-auto w-full')}>
              <AutoScroll
                activeIndex={categoryList.findIndex(
                  it => it.value === activeTab
                )}
                wrapClassName="!justify-start"
                className=""
                listKey="categoryList"
              >
                <div className="flex gap-[16px]">
                  {categoryList?.map(it => (
                    <div
                      className={classNames(
                        'text-[14px] font-medium pb-[6px] cursor-pointer whitespace-nowrap leading-none text-white',
                        {
                          categoryItemActive: it.value === activeTab,
                        }
                      )}
                      key={it.value}
                      onClick={() => setActiveTab(it.value)}
                    >
                      {it.label}
                    </div>
                  ))}
                </div>
              </AutoScroll>
            </div>
          )}
        </div>
        <MultipleTemplateList
          activeGender={activeGender}
          activeTemplate={activeTemplate}
          setActiveTemplate={setActiveTemplate}
          selectTemplateList={selectTemplateList}
          listKey={`${activeGender}-${activeTab}`}
          multiline={screenOrientation.isPortrait}
        />
        <CreateBtn
          activeTemplate={activeTemplate}
          activeGender={activeGender}
        />
      </div>
      <ThemeDetailModal
        activeGender={activeGender}
        open={themeDetailModalOpen}
        setOpen={setThemeDetailModalOpen}
        themeDetail={activeTemplate}
      />
    </>
  )
}
