import { toCDNImage } from '@/utils'
import { TemplateListProps } from './const'
import SingleTemplate from './SingleTemplate'
import { useTranslation } from 'react-i18next'

/** 多行模版列表 - 简单平铺布局 */
const MultipleTemplateList = ({
  selectTemplateList,
  activeTemplate,
  setActiveTemplate,
  activeGender,
}: TemplateListProps) => {
  const { t } = useTranslation()

  return (
    <>
      {selectTemplateList && selectTemplateList.length > 0 ? (
        <div className="grid grid-cols-2 gap-8 py-2">
          {selectTemplateList.map(template => (
            <div key={template.id} className="w-full">
              <SingleTemplate
                isMultiple
                activeGender={activeGender}
                item={template}
                active={template.id === activeTemplate?.id}
                onSelect={() => setActiveTemplate(template)}
              />
            </div>
          ))}
        </div>
      ) : (
        <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 my-[34px] p-4 text-center">
          <img
            className="w-24"
            src={toCDNImage('/images/common/grinning.png')}
            alt=""
          />
          <div className="text-xl font-bold maze-primary-text mt-6">
            {t('当前分类下还没有模板')}
            <br />
            {t('去看看别的分类吧')}
          </div>
        </div>
      )}
    </>
  )
}
export default MultipleTemplateList
