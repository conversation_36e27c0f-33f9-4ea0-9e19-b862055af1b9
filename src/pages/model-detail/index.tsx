import React, { useState, useMemo, useEffect } from 'react'
import { useSearchParams } from 'react-router-dom'
import { MyContainer } from '@/components/ui/MyContainer'
import { CommonNavBar } from '@/components/ui/CommonNavBar'
import { useTranslation } from 'react-i18next'
import { useAtom, useAtomValue } from 'jotai'
import { selectedGenderAtom, screenOrientationAtom } from '@/stores'
import { ThemeDetail } from '@/apis/types'
import _ajax from '@/utils/ajax'
import _api from '@/apis/maze.api'
import useSWR from 'swr'
import { MirrorLoading } from 'wujieai-react-icon'
import MazeSingleTemplateList from '@/components/pages/homepage/MazeSingleTemplateList'
import { toCDNImage } from '@/utils'

/**
 * 模型详情页面
 * 显示当前模型包含的主题列表
 */
const ModelDetail: React.FC = () => {
  const [searchParams] = useSearchParams()
  const { t } = useTranslation()
  const screenOrientation = useAtomValue(screenOrientationAtom)
  const [activeGender] = useAtom(selectedGenderAtom)

  // 从URL参数获取模型信息
  const modelId = searchParams.get('id')
  const modelName = searchParams.get('name') || t('模型详情')

  // Swiper实例引用
  const [thumbsSwiper, setThumbsSwiper] = useState<SwiperType | null>(null)
  const [activeIndex, setActiveIndex] = useState(0)

  // 获取主题详情数据
  const { data: themeDetailData, isLoading } = useSWR(
    modelId ? [modelId] : null,
    ([id]) => _ajax.get(_api.theme_detail, { params: { id } })
  )

  // 过滤并处理主题列表数据
  const detailList = useMemo(() => {
    const itemList = themeDetailData?.data?.data?.itemList || []
    return itemList.filter((item: { sex: string }) => {
      return item.sex === activeGender
    })
  }, [themeDetailData, activeGender])

  // 设置默认选中第一个
  useEffect(() => {
    if (detailList.length > 0) {
      setActiveIndex(0)
    }
  }, [detailList])

  return (
    <MyContainer>
      <div className="w-full h-full bg-[#141828] flex flex-col">
        {/* 通用导航栏 */}
        <CommonNavBar
          title={modelName}
          className="sticky top-0 z-10"
        />

        {/* 页面内容区域 */}
        <div className="flex-1 relative overflow-hidden">
          {isLoading && (
            <div className="flex items-center justify-center w-full h-full">
              <MirrorLoading className="animate-spin maze-primary-text w-12 h-12" />
            </div>
          )}

          {!isLoading && detailList.length === 0 && (
            <div className="flex items-center justify-center w-full h-full">
              <div className="text-center text-white">
                <div className="text-xl font-bold opacity-65">
                  {t('当前分类下还没有模板')}
                </div>
                <div className="text-lg mt-2 opacity-65">
                  {t('去看看别的分类吧')}
                </div>
              </div>
            </div>
          )}

          {!isLoading && detailList.length > 0 && (
            <>
              {/* 主要Swiper展示区域 */}
              <div className="relative h-[70vh] w-full">
                <Swiper
                  modules={[Navigation, Pagination, Thumbs]}
                  spaceBetween={10}
                  slidesPerView={1}
                  navigation={{
                    nextEl: '.swiper-button-next-custom',
                    prevEl: '.swiper-button-prev-custom',
                  }}
                  thumbs={{ swiper: thumbsSwiper }}
                  onSlideChange={(swiper) => setActiveIndex(swiper.activeIndex)}
                  className="h-full w-full"
                >
                  {detailList.map((item: any, index: number) => (
                    <SwiperSlide key={index}>
                      <div className="flex items-center justify-center h-full p-4">
                        <div className="relative max-w-md w-full">
                          <MyImage
                            src={item.image}
                            tag="v800"
                            className="w-full rounded-lg"
                            imgClassName="object-cover"
                            style={{ aspectRatio: '0.66' }}
                          />
                          <div className="absolute bottom-0 left-0 right-0 h-16 bg-gradient-to-t from-black/80 to-transparent rounded-b-lg">
                            <h2 className="text-white text-lg font-semibold text-center leading-[4rem] px-4 truncate">
                              {item.name}
                            </h2>
                          </div>
                        </div>
                      </div>
                    </SwiperSlide>
                  ))}
                </Swiper>

                {/* 自定义导航按钮 */}
                {detailList.length > 1 && (
                  <>
                    <div className="swiper-button-prev-custom absolute left-4 top-1/2 -translate-y-1/2 z-10 cursor-pointer hover:scale-110 transition-transform duration-300">
                      <SvgIcon
                        src="/images/icons/arrow-left.svg"
                        alt="上一页"
                        className="w-12 h-12"
                        svgClassName="text-white"
                      />
                    </div>
                    <div className="swiper-button-next-custom absolute right-4 top-1/2 -translate-y-1/2 z-10 cursor-pointer hover:scale-110 transition-transform duration-300">
                      <SvgIcon
                        src="/images/icons/arrow-right.svg"
                        alt="下一页"
                        className="w-12 h-12"
                        svgClassName="text-white"
                      />
                    </div>
                  </>
                )}
              </div>

              {/* 缩略图导航 */}
              {detailList.length > 1 && (
                <div className="h-[20vh] px-4 py-2">
                  <Swiper
                    onSwiper={setThumbsSwiper}
                    spaceBetween={8}
                    slidesPerView={screenOrientation.isLandScape ? 6 : 4}
                    freeMode={true}
                    watchSlidesProgress={true}
                    modules={[Thumbs]}
                    className="h-full"
                  >
                    {detailList.map((item: any, index: number) => (
                      <SwiperSlide key={index}>
                        <div
                          className={classNames(
                            'relative w-full h-full rounded-lg overflow-hidden cursor-pointer transition-all duration-300',
                            {
                              'ring-2 ring-blue-500 scale-105': index === activeIndex,
                              'opacity-70 hover:opacity-100': index !== activeIndex,
                            }
                          )}
                        >
                          <MyImage
                            src={item.image}
                            tag="v400"
                            className="w-full h-full"
                            imgClassName="object-cover"
                          />
                        </div>
                      </SwiperSlide>
                    ))}
                  </Swiper>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </MyContainer>
  )
}

export default ModelDetail
