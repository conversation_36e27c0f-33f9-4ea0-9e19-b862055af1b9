import React, { useState, useMemo, useEffect } from 'react'
import { useSearchParams } from 'react-router-dom'
import { MyContainer } from '@/components/ui/MyContainer'
import { CommonNavBar } from '@/components/ui/CommonNavBar'
import { useTranslation } from 'react-i18next'
import { useAtom, useAtomValue } from 'jotai'
import { selectedGenderAtom, screenOrientationAtom } from '@/stores'
import { ThemeDetail } from '@/apis/types'
import _ajax from '@/utils/ajax'
import _api from '@/apis/maze.api'
import useSWR from 'swr'
import { MirrorLoading } from 'wujieai-react-icon'
import MazeSingleTemplateList from '@/components/pages/homepage/MazeSingleTemplateList'
import { CreateBtn } from '@/components/pages/homepage/CreateBtn'
import { toCDNImage } from '@/utils'

/**
 * 模型详情页面
 * 显示当前模型包含的主题列表
 */
const ModelDetail: React.FC = () => {
  const [searchParams] = useSearchParams()
  const { t } = useTranslation()
  const screenOrientation = useAtomValue(screenOrientationAtom)
  const [activeGender] = useAtom(selectedGenderAtom)

  // 从URL参数获取模型信息
  const modelId = searchParams.get('id')
  const modelName = searchParams.get('name') || t('模型详情')

  // 当前选中的模板
  const [activeTemplate, setActiveTemplate] = useState<ThemeDetail | null | undefined>(null)

  // 获取主题详情数据
  const { data: themeDetailData, isLoading } = useSWR(
    modelId ? [modelId] : null,
    ([id]) => _ajax.get(_api.theme_detail, { params: { id } })
  )

  // 过滤并处理主题列表数据，转换为ThemeDetail格式
  const detailList = useMemo(() => {
    const itemList = themeDetailData?.data?.data?.itemList || []
    const filteredList = itemList.filter((item: { sex: string }) => {
      return item.sex === activeGender
    })

    // 将itemList转换为ThemeDetail格式以兼容MazeSingleTemplateList组件
    return filteredList.map((item: any) => ({
      id: item.id,
      name: item.name,
      cover_image: item.image,
      cover_image_female: item.image,
      template_count: 1,
      type: 0,
      price: item.price || 0,
      video: {
        resultUrl: ''
      }
    })) as ThemeDetail[]
  }, [themeDetailData, activeGender])

  // 设置默认选中第一个
  useEffect(() => {
    if (detailList.length > 0) {
      setActiveTemplate(detailList[0])
    }
  }, [detailList])

  return (
    <MyContainer>
      <div className="w-full h-full bg-[#141828] flex flex-col">
        {/* 通用导航栏 */}
        <CommonNavBar
          title={modelName}
          className="sticky top-0 z-10"
        />

        {/* 页面内容区域 */}
        <div className="flex-1 relative">
          {isLoading && (
            <div className="flex items-center justify-center w-full h-full">
              <MirrorLoading className="animate-spin maze-primary-text w-12 h-12" />
            </div>
          )}

          {!isLoading && detailList.length === 0 && (
            <div className="flex items-center justify-center w-full h-full">
              <div className="text-center text-white">
                <div className="text-xl font-bold opacity-65">
                  {t('当前分类下还没有模板')}
                </div>
                <div className="text-lg mt-2 opacity-65">
                  {t('去看看别的分类吧')}
                </div>
              </div>
            </div>
          )}

          {!isLoading && detailList.length > 0 && (
            <div className="h-full relative">
              {/* 使用MazeSingleTemplateList组件展示主题列表 */}
              <MazeSingleTemplateList
                selectTemplateList={detailList}
                activeTemplate={activeTemplate}
                setActiveTemplate={setActiveTemplate}
                listKey={`model-detail-${modelId}-${activeGender}`}
                multiline={false}
                activeGender={activeGender}
                className="h-full pb-32"
                swiperProps={{
                  slidesPerView: screenOrientation.isLandScape ? 1.5 : 1.2,
                  spaceBetween: 20,
                  centeredSlides: true,
                  loop: detailList.length > 1,
                }}
              />

              {/* 底部创建按钮 */}
              <CreateBtn
                activeTemplate={activeTemplate}
                activeGender={activeGender}
                isPortrait={screenOrientation.isPortrait}
              />
            </div>
          )}
        </div>
      </div>
    </MyContainer>
  )
}

export default ModelDetail
